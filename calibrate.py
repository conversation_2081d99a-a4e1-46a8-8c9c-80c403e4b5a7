from ultralytics import YOLO
import cv2
import freenect
import numpy as np
# Load YOLOv8 model
model = YOLO("best.pt")

# Get a frame from Kinect v1 using freenect
def get_kinect_frame(): 
    depth, _ = freenect.sync_get_depth()
    video, _ = freenect.sync_get_video()
    video = cv2.cvtColor(video, cv2.COLOR_RGB2BGR)
    return video

while True:
    frame = get_kinect_frame()
    frame = cv2.resize(frame, (640, 480))
    frame_height, frame_width = frame.shape[:2]
    center_x = frame_width // 2
    center_y = frame_height // 2

    # YOLOv8 tracking
    results = model.track(source=frame, conf=0.1, persist=True, tracker="bytetrack.yaml", stream=True)

    for result in results:
        boxes = result.boxes
        for box in boxes:
            x1, y1, x2, y2 = map(int, box.xyxy[0])
            cx = (x1 + x2) // 2
            cy = (y1 + y2) // 2
            id = int(box.id[0]) if box.id is not None else -1

            print(f"ID: {id} | X: {cx} | Y: {cy}")
            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
            cv2.circle(frame, (cx, cy), 5, (0, 0, 255), -1)
            label = f"ID: {id} | X: {cx} | Y: {cy}"
            cv2.putText(frame, label, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)

    # Draw quadrants
    cv2.line(frame, (center_x, 0), (center_x, frame_height), (255, 0, 0), 2)
    cv2.line(frame, (0, center_y), (frame_width, center_y), (255, 0, 0), 2)

    # Show frame
    cv2.imshow("YOLOv8 Quadrant + Coordinates", frame)
    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

cv2.destroyAllWindows()
