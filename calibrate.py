import cv2
import freenect
import numpy as np

# تخزين وتتبع الكائنات للحصول على IDs ثابتة
object_memory = {}
next_id = 0

# مولد الألوان الفريدة لكل ID
def generate_unique_color(obj_id):
    """توليد لون فريد لكل ID بناءً على رقم الكائن"""
    # استخدام HSV لتوليد ألوان متنوعة ومميزة
    import colorsys

    # تحويل ID إلى قيمة hue (0-1)
    hue = (obj_id * 0.618033988749895) % 1.0  # النسبة الذهبية لتوزيع أفضل
    saturation = 0.9  # تشبع عالي للألوان الزاهية
    value = 0.9  # سطوع عالي

    # تحويل من HSV إلى RGB
    rgb = colorsys.hsv_to_rgb(hue, saturation, value)

    # تحويل إلى BGR للاستخدام مع OpenCV (القيم من 0-255)
    bgr = (int(rgb[2] * 255), int(rgb[1] * 255), int(rgb[0] * 255))

    return bgr

# Get a frame from Kinect v1 using freenect
def get_kinect_frame():
    _, _ = freenect.sync_get_depth()
    video, _ = freenect.sync_get_video()
    video = cv2.cvtColor(video, cv2.COLOR_RGB2BGR)
    return video

# تعيين ID ثابت للكائنات بناءً على المسافة
def assign_stable_id(center, radius, memory, distance_threshold=50):
    global next_id
    center_array = np.array(center)

    closest_id = None
    min_dist = distance_threshold

    # البحث عن أقرب كائن موجود
    for obj_id, obj_data in memory.items():
        prev_center = np.array([obj_data['center_x'], obj_data['center_y']])
        dist = np.linalg.norm(center_array - prev_center)

        # التحقق من المسافة ونصف القطر المتشابه
        radius_diff = abs(radius - obj_data['radius'])
        if dist < min_dist and radius_diff < 10:  # تسامح في نصف القطر
            min_dist = dist
            closest_id = obj_id

    if closest_id is not None:
        # تحديث الكائن الموجود مع تنعيم الإحداثيات
        old_data = memory[closest_id]
        smoothing_factor = 0.7  # عامل التنعيم (0.7 = 70% من القيمة القديمة)

        memory[closest_id] = {
            'center_x': int(old_data['center_x'] * smoothing_factor + center[0] * (1 - smoothing_factor)),
            'center_y': int(old_data['center_y'] * smoothing_factor + center[1] * (1 - smoothing_factor)),
            'radius': int(old_data['radius'] * smoothing_factor + radius * (1 - smoothing_factor)),
            'last_seen': 0,  # إعادة تعيين العداد
            'stable': old_data.get('stable', False) or old_data.get('last_seen', 0) < 5
        }
        return closest_id
    else:
        # إنشاء كائن جديد
        memory[next_id] = {
            'center_x': center[0],
            'center_y': center[1],
            'radius': radius,
            'last_seen': 0,
            'stable': False
        }
        current_id = next_id
        next_id += 1
        return current_id

# تنظيف الكائنات التي لم تعد مرئية
def cleanup_memory(memory, max_frames_missing=30):
    to_remove = []
    for obj_id, obj_data in memory.items():
        obj_data['last_seen'] += 1
        if obj_data['last_seen'] > max_frames_missing:
            to_remove.append(obj_id)

    for obj_id in to_remove:
        del memory[obj_id]

# كشف الدوائر باستخدام HoughCircles - مخصص للدوائر بحجم 3.5 سم
def detect_circles(frame):
    # تحويل الإطار إلى رمادي
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

    # تطبيق تنعيم للتقليل من الضوضاء
    gray = cv2.medianBlur(gray, 5)

    # حساب نصف القطر المتوقع للدوائر بحجم 3.5 سم
    # افتراض: 1 سم ≈ 12 بكسل (قد تحتاج لضبط هذا حسب المسافة من الكاميرا)
    target_radius_cm = 3.5 / 2  # نصف القطر = 1.75 سم
    pixels_per_cm = 12  # قيمة تقريبية - قد تحتاج للضبط
    target_radius_pixels = int(target_radius_cm * pixels_per_cm)

    # نطاق مسموح ±20% من الحجم المستهدف
    tolerance = 0.2
    min_radius = int(target_radius_pixels * (1 - tolerance))
    max_radius = int(target_radius_pixels * (1 + tolerance))

    # كشف الدوائر باستخدام HoughCircles
    circles = cv2.HoughCircles(
        gray,
        cv2.HOUGH_GRADIENT,
        dp=1,
        minDist=int(target_radius_pixels * 1.5),  # المسافة الدنيا بين مراكز الدوائر
        param1=50,   # العتبة العليا لكاشف الحواف
        param2=25,   # عتبة التراكم لمراكز الدوائر
        minRadius=min_radius,  # نصف قطر أدنى للدوائر 3.5 سم
        maxRadius=max_radius   # نصف قطر أعلى للدوائر 3.5 سم
    )

    raw_detections = []
    if circles is not None:
        circles = np.round(circles[0, :]).astype("int")
        for (x, y, r) in circles:
            raw_detections.append({
                'center_x': x,
                'center_y': y,
                'radius': r
            })

    return raw_detections

while True:
    frame = get_kinect_frame()
    frame = cv2.resize(frame, (640, 480))
    frame_height, frame_width = frame.shape[:2]
    center_x = frame_width // 2
    center_y = frame_height // 2

    # كشف الدوائر بحجم 3.5 سم
    raw_detections = detect_circles(frame)

    # تعيين IDs ثابتة للكائنات المكتشفة
    current_frame_ids = []
    for detection in raw_detections:
        center = (detection['center_x'], detection['center_y'])
        radius = detection['radius']
        stable_id = assign_stable_id(center, radius, object_memory)
        current_frame_ids.append(stable_id)

    # تنظيف الذاكرة من الكائنات المفقودة
    cleanup_memory(object_memory)

    # رسم الكائنات المتتبعة مع الإحداثيات الثابتة
    for obj_id, obj_data in object_memory.items():
        if obj_data['last_seen'] <= 5:  # عرض الكائنات المرئية حديثاً فقط
            cx, cy = obj_data['center_x'], obj_data['center_y']
            radius = obj_data['radius']
            is_stable = obj_data['stable']

            # حساب الصندوق المحيط
            x1 = max(0, cx - radius)
            y1 = max(0, cy - radius)
            x2 = min(frame.shape[1], cx + radius)
            y2 = min(frame.shape[0], cy + radius)

            # توليد لون فريد لهذا الكائن
            unique_color = generate_unique_color(obj_id)

            # طباعة معلومات الدائرة (فقط للكائنات الثابتة)
            if is_stable:
                diameter_pixels = radius * 2
                estimated_diameter_cm = diameter_pixels / 12
                print(f"ID: {obj_id} | X: {cx} | Y: {cy} | Radius: {radius}px | Est. Diameter: {estimated_diameter_cm:.1f}cm | STABLE | Color: {unique_color}")

            # اختيار الألوان بناءً على الاستقرار مع الحفاظ على اللون الفريد
            if is_stable:
                rect_color = unique_color  # لون فريد للصندوق
                circle_color = unique_color  # نفس اللون للدائرة
                center_color = unique_color  # نفس اللون لنقطة المركز
                text_color = unique_color  # نفس اللون للنص
                status = "STABLE"
            else:
                # للكائنات غير الثابتة، استخدم لون باهت من نفس اللون الفريد
                faded_color = (int(unique_color[0] * 0.6), int(unique_color[1] * 0.6), int(unique_color[2] * 0.6))
                rect_color = faded_color
                circle_color = faded_color
                center_color = faded_color
                text_color = faded_color
                status = "TRACKING"

            # رسم الصندوق المحيط والدائرة بألوان فريدة
            cv2.rectangle(frame, (x1, y1), (x2, y2), rect_color, 3)  # خط أسمك للوضوح
            cv2.circle(frame, (cx, cy), radius, circle_color, 3)  # رسم محيط الدائرة
            cv2.circle(frame, (cx, cy), 8, center_color, -1)  # نقطة المركز أكبر وبنفس اللون

            # إضافة حلقة بيضاء حول نقطة المركز للوضوح
            cv2.circle(frame, (cx, cy), 10, (255, 255, 255), 2)

            # عرض المعلومات مع خلفية للوضوح
            diameter_pixels = radius * 2
            estimated_diameter_cm = diameter_pixels / 12
            label = f"ID:{obj_id} | X:{cx} Y:{cy} | {estimated_diameter_cm:.1f}cm | {status}"

            # رسم خلفية للنص لتحسين الوضوح
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
            cv2.rectangle(frame, (x1, y1 - 25), (x1 + label_size[0] + 5, y1 - 5), (0, 0, 0), -1)
            cv2.putText(frame, label, (x1 + 2, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, text_color, 2)

    # Draw quadrants
    cv2.line(frame, (center_x, 0), (center_x, frame_height), (255, 0, 0), 2)
    cv2.line(frame, (0, center_y), (frame_width, center_y), (255, 0, 0), 2)

    # Show frame
    cv2.imshow("Circle Detection (3.5cm) + Coordinates", frame)
    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

cv2.destroyAllWindows()
