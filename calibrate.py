import cv2
import freenect
import numpy as np

# Get a frame from Kinect v1 using freenect
def get_kinect_frame():
    _, _ = freenect.sync_get_depth()
    video, _ = freenect.sync_get_video()
    video = cv2.cvtColor(video, cv2.COLOR_RGB2BGR)
    return video

# كشف الدوائر باستخدام HoughCircles - مخصص للدوائر بحجم 3.5 سم
def detect_circles(frame):
    # تحويل الإطار إلى رمادي
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

    # تطبيق تنعيم للتقليل من الضوضاء
    gray = cv2.medianBlur(gray, 5)

    # حساب نصف القطر المتوقع للدوائر بحجم 3.5 سم
    # افتراض: 1 سم ≈ 12 بكسل (قد تحتاج لضبط هذا حسب المسافة من الكاميرا)
    target_radius_cm = 3.5 / 2  # نصف القطر = 1.75 سم
    pixels_per_cm = 12  # قيمة تقريبية - قد تحتاج للضبط
    target_radius_pixels = int(target_radius_cm * pixels_per_cm)

    # نطاق مسموح ±20% من الحجم المستهدف
    tolerance = 0.2
    min_radius = int(target_radius_pixels * (1 - tolerance))
    max_radius = int(target_radius_pixels * (1 + tolerance))

    # كشف الدوائر باستخدام HoughCircles
    circles = cv2.HoughCircles(
        gray,
        cv2.HOUGH_GRADIENT,
        dp=1,
        minDist=int(target_radius_pixels * 1.5),  # المسافة الدنيا بين مراكز الدوائر
        param1=50,   # العتبة العليا لكاشف الحواف
        param2=25,   # عتبة التراكم لمراكز الدوائر
        minRadius=min_radius,  # نصف قطر أدنى للدوائر 3.5 سم
        maxRadius=max_radius   # نصف قطر أعلى للدوائر 3.5 سم
    )

    detected_circles = []
    if circles is not None:
        circles = np.round(circles[0, :]).astype("int")
        for i, (x, y, r) in enumerate(circles):
            # إنشاء ID فريد لكل دائرة (بسيط للمعايرة)
            circle_id = i + 1
            detected_circles.append({
                'id': circle_id,
                'center_x': x,
                'center_y': y,
                'radius': r,
                'x1': max(0, x - r),
                'y1': max(0, y - r),
                'x2': min(frame.shape[1], x + r),
                'y2': min(frame.shape[0], y + r)
            })

    return detected_circles

while True:
    frame = get_kinect_frame()
    frame = cv2.resize(frame, (640, 480))
    frame_height, frame_width = frame.shape[:2]
    center_x = frame_width // 2
    center_y = frame_height // 2

    # كشف الدوائر بحجم 3.5 سم
    detected_circles = detect_circles(frame)

    for circle in detected_circles:
        x1, y1, x2, y2 = circle['x1'], circle['y1'], circle['x2'], circle['y2']
        cx, cy = circle['center_x'], circle['center_y']
        circle_id = circle['id']
        radius = circle['radius']

        # طباعة معلومات الدائرة
        diameter_pixels = radius * 2
        estimated_diameter_cm = diameter_pixels / 12  # استخدام نفس pixels_per_cm
        print(f"ID: {circle_id} | X: {cx} | Y: {cy} | Radius: {radius}px | Est. Diameter: {estimated_diameter_cm:.1f}cm")

        # رسم الصندوق المحيط والدائرة
        cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
        cv2.circle(frame, (cx, cy), radius, (255, 0, 255), 2)  # رسم محيط الدائرة
        cv2.circle(frame, (cx, cy), 5, (0, 0, 255), -1)  # نقطة المركز

        label = f"ID: {circle_id} | X: {cx} | Y: {cy} | {estimated_diameter_cm:.1f}cm"
        cv2.putText(frame, label, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)

    # Draw quadrants
    cv2.line(frame, (center_x, 0), (center_x, frame_height), (255, 0, 0), 2)
    cv2.line(frame, (0, center_y), (frame_width, center_y), (255, 0, 0), 2)

    # Show frame
    cv2.imshow("Circle Detection (3.5cm) + Coordinates", frame)
    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

cv2.destroyAllWindows()
