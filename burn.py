from ultralytics import YOLO
import cv2
import freenect
import numpy as np
import time
import pyfirmata

# تحميل نموذج YOLOv8
model = YOLO("best.pt")

# إعداد Arduino عبر pyfirmata
board = pyfirmata.Arduino('/dev/ttyACM0')  # غيّر المسار حسب جهازك
it = pyfirmata.util.Iterator(board)
it.start()

# تعريف منافذ السيرفو والليزر
servo_x = board.get_pin('d:9:s')     # D9: Servo X
servo_y = board.get_pin('d:10:s')    # D10: Servo Y
laser = board.get_pin('d:8:o')       # D8: Laser ON/OFF

# التقاط إطار من Kinect v1
def get_kinect_frame():
    _, _ = freenect.sync_get_depth()
    video, _ = freenect.sync_get_video()
    return cv2.cvtColor(video, cv2.COLOR_RGB2BGR)

# تخزين وتتبع الكائنات
object_memory = {}
burned_ids = []
next_id = 0

def compute_center(box):
    x1, y1, x2, y2 = box
    return np.array([(x1 + x2) / 2, (y1 + y2) / 2])

def assign_id(center, memory, threshold=50):
    global next_id
    closest_id = None
    min_dist = threshold
    for obj_id, prev_center in memory.items():
        dist = np.linalg.norm(center - prev_center)
        if dist < min_dist:
            min_dist = dist
            closest_id = obj_id
    if closest_id is not None:
        memory[closest_id] = center
        return closest_id
    memory[next_id] = center
    next_id += 1
    return next_id - 1

# الحلقة الرئيسية
while True:
    frame = get_kinect_frame()
    frame = cv2.resize(frame, (640, 480))
    frame_height, frame_width = frame.shape[:2]
    center_x = frame_width // 2
    center_y = frame_height // 2

    results = model.track(source=frame, conf=0.3, persist=True, tracker="bytetrack.yaml", stream=True)

    for result in results:
        boxes = result.boxes
        if boxes is None:
            continue

        for box in boxes:
            x1, y1, x2, y2 = map(int, box.xyxy[0])
            center = compute_center((x1, y1, x2, y2))
            obj_id = assign_id(center, object_memory)

            # تحويل المركز إلى زوايا للسيرفو
            box_center_x, box_center_y = int(center[0]), int(center[1])
            servo_angle_x = 180 - int((box_center_x * 105) / 320)
            servo_angle_x = max(80, min(110, servo_angle_x))
            servo_angle_y = int((box_center_y * 95) / 240)
            servo_angle_y = max(90, min(115, servo_angle_y))

            if obj_id in burned_ids:
                color = (0, 0, 255)
                label = f"ID:{obj_id} (burned)"
            else:
                # توجيه السيرفو وتفعيل الليزر
                servo_x.write(servo_angle_x)
                servo_y.write(servo_angle_y)
                laser.write(1)
                print(f"🎯 استهداف ID:{obj_id} عند الزوايا X:{servo_angle_x}°, Y:{servo_angle_y}°")
                time.sleep(2)
                laser.write(0)
                burned_ids.append(obj_id)
                color = (0, 255, 0)
                label = f"ID:{obj_id}"

            # رسم الصندوق والتسمية
            cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
            cv2.putText(frame, label, (x1, y1 - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

    # رسم خطوط التقسيم
    cv2.line(frame, (center_x, 0), (center_x, frame_height), (255, 0, 0), 2)
    cv2.line(frame, (0, center_y), (frame_width, center_y), (255, 0, 0), 2)

    # عرض الإطار
    cv2.imshow("YOLOv8 + Arduino + Kinect", frame)
    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

# عند الخروج: إعادة السيرفو لمكانه وإيقاف الليزر
servo_x.write(90)
servo_y.write(90)
laser.write(0)
board.exit()
cv2.destroyAllWindows()
